const { body, param, query, validationResult } = require('express-validator');
const blockchainService = require('../services/blockchainService');

/**
 * Validation middleware using express-validator
 */

// Handle validation results
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));
    
    return res.validationError(formattedErrors, 'Validation failed');
  }
  
  next();
};

// Common validation rules
const validationRules = {
  // Address validation
  address: () => [
    body('address')
      .notEmpty()
      .withMessage('Address is required')
      .custom((value) => {
        if (!blockchainService.isValidAddress(value)) {
          throw new Error('Invalid blockchain address format');
        }
        return true;
      })
  ],

  addressParam: (paramName = 'address') => [
    param(paramName)
      .notEmpty()
      .withMessage(`${paramName} is required`)
      .custom((value) => {
        if (!blockchainService.isValidAddress(value)) {
          throw new Error(`Invalid ${paramName} format`);
        }
        return true;
      })
  ],

  // User profile validation
  userProfile: () => [
    body('name')
      .notEmpty()
      .withMessage('Name is required')
      .isLength({ min: 2, max: 100 })
      .withMessage('Name must be between 2 and 100 characters')
      .matches(/^[\u4e00-\u9fa5a-zA-Z\s]+$/)
      .withMessage('Name can only contain Chinese characters, letters, and spaces'),
    
    body('phone')
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('Invalid phone number format'),
    
    body('idNumber')
      .optional()
      .matches(/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/)
      .withMessage('Invalid ID number format')
  ],

  // Role validation
  userRole: () => [
    body('role')
      .isIn(['user', 'reviewer', 'admin'])
      .withMessage('Role must be user, reviewer, or admin'),
    
    body('updatedBy')
      .notEmpty()
      .withMessage('UpdatedBy address is required')
      .custom((value) => {
        if (!blockchainService.isValidAddress(value)) {
          throw new Error('Invalid updatedBy address format');
        }
        return true;
      })
  ],

  // Patent upload validation
  patentUpload: () => [
    body('patentName')
      .notEmpty()
      .withMessage('Patent name is required')
      .isLength({ min: 5, max: 200 })
      .withMessage('Patent name must be between 5 and 200 characters'),
    
    body('patentNumber')
      .notEmpty()
      .withMessage('Patent number is required')
      .matches(/^CN\d{9,15}(\.\d)?$/)
      .withMessage('Invalid patent number format (expected: CN followed by 9-15 digits, optionally with .digit)'),
    
    body('patentCategory')
      .notEmpty()
      .withMessage('Patent category is required')
      .isLength({ min: 2, max: 50 })
      .withMessage('Patent category must be between 2 and 50 characters'),
    
    body('transferPrice')
      .isNumeric()
      .withMessage('Transfer price must be a number')
      .isFloat({ min: 1 })
      .withMessage('Transfer price must be greater than 0'),
    
    body('patentAbstract')
      .notEmpty()
      .withMessage('Patent abstract is required')
      .isLength({ min: 50, max: 2000 })
      .withMessage('Patent abstract must be between 50 and 2000 characters'),
    
    body('applicationDate')
      .isISO8601()
      .withMessage('Invalid application date format')
      .custom((value) => {
        const date = new Date(value);
        const now = new Date();
        if (date > now) {
          throw new Error('Application date cannot be in the future');
        }
        return true;
      }),
    
    body('expirationDate')
      .isISO8601()
      .withMessage('Invalid expiration date format')
      .custom((value, { req }) => {
        const expirationDate = new Date(value);
        const applicationDate = new Date(req.body.applicationDate);
        const now = new Date();
        
        if (expirationDate <= now) {
          throw new Error('Expiration date must be in the future');
        }
        
        if (expirationDate <= applicationDate) {
          throw new Error('Expiration date must be after application date');
        }
        
        return true;
      }),
    
    body('ownerName')
      .notEmpty()
      .withMessage('Owner name is required')
      .isLength({ min: 2, max: 100 })
      .withMessage('Owner name must be between 2 and 100 characters'),
    
    body('ownerIdNumber')
      .notEmpty()
      .withMessage('Owner ID number is required')
      .matches(/^[1-9]\d{5}(18|19|20|21)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/)
      .withMessage('Invalid owner ID number format'),
    
    body('isAgentSale')
      .custom((value) => {
        // Handle both boolean and string representations
        if (value === true || value === false || value === 'true' || value === 'false') {
          return true;
        }
        throw new Error('isAgentSale must be a boolean or boolean string');
      }),
    
    body('uploaderAddress')
      .notEmpty()
      .withMessage('Uploader address is required')
      .custom((value) => {
        if (!blockchainService.isValidAddress(value)) {
          throw new Error('Invalid uploader address format');
        }
        return true;
      })
  ],

  // Transaction validation
  transactionInitiate: () => [
    body('patentId')
      .isNumeric()
      .withMessage('Patent ID must be a number')
      .isInt({ min: 1 })
      .withMessage('Patent ID must be a positive integer'),
    
    body('buyerAddress')
      .notEmpty()
      .withMessage('Buyer address is required')
      .custom((value) => {
        if (!blockchainService.isValidAddress(value)) {
          throw new Error('Invalid buyer address format');
        }
        return true;
      }),
    
    body('sellerAddress')
      .notEmpty()
      .withMessage('Seller address is required')
      .custom((value) => {
        if (!blockchainService.isValidAddress(value)) {
          throw new Error('Invalid seller address format');
        }
        return true;
      }),
    
    body('price')
      .isNumeric()
      .withMessage('Price must be a number')
      .isFloat({ min: 1 })
      .withMessage('Price must be greater than 0')
  ],

  // Review validation
  reviewAction: () => [
    body('reviewerAddress')
      .notEmpty()
      .withMessage('Reviewer address is required')
      .custom((value) => {
        if (!blockchainService.isValidAddress(value)) {
          throw new Error('Invalid reviewer address format');
        }
        return true;
      }),
    
    body('comments')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('Comments must not exceed 1000 characters')
  ],

  reviewReject: () => [
    body('reviewerAddress')
      .notEmpty()
      .withMessage('Reviewer address is required')
      .custom((value) => {
        if (!blockchainService.isValidAddress(value)) {
          throw new Error('Invalid reviewer address format');
        }
        return true;
      }),
    
    body('reason')
      .notEmpty()
      .withMessage('Rejection reason is required')
      .isLength({ min: 10, max: 1000 })
      .withMessage('Rejection reason must be between 10 and 1000 characters')
  ],

  // Protection request validation
  protectionRequest: () => [
    body('patentAddress')
      .notEmpty()
      .withMessage('Patent address is required')
      .custom((value) => {
        if (!blockchainService.isValidAddress(value)) {
          throw new Error('Invalid patent address format');
        }
        return true;
      }),
    
    body('patentName')
      .notEmpty()
      .withMessage('Patent name is required')
      .isLength({ min: 5, max: 200 })
      .withMessage('Patent name must be between 5 and 200 characters'),
    
    body('applicantAddress')
      .notEmpty()
      .withMessage('Applicant address is required')
      .custom((value) => {
        if (!blockchainService.isValidAddress(value)) {
          throw new Error('Invalid applicant address format');
        }
        return true;
      }),
    
    body('description')
      .notEmpty()
      .withMessage('Description is required')
      .isLength({ min: 50, max: 2000 })
      .withMessage('Description must be between 50 and 2000 characters'),
    
    body('evidenceFiles')
      .optional()
      .isArray()
      .withMessage('Evidence files must be an array')
  ],

  // Notification validation
  notification: () => [
    body('recipientAddress')
      .notEmpty()
      .withMessage('Recipient address is required')
      .custom((value) => {
        if (!blockchainService.isValidAddress(value)) {
          throw new Error('Invalid recipient address format');
        }
        return true;
      }),
    
    body('type')
      .isIn(['patent_approved', 'patent_rejected', 'transaction_initiated', 'transaction_approved', 'transaction_rejected', 'transaction_completed', 'protection_requested', 'protection_approved', 'protection_rejected', 'system_announcement'])
      .withMessage('Invalid notification type'),
    
    body('title')
      .notEmpty()
      .withMessage('Title is required')
      .isLength({ min: 5, max: 100 })
      .withMessage('Title must be between 5 and 100 characters'),
    
    body('message')
      .notEmpty()
      .withMessage('Message is required')
      .isLength({ min: 10, max: 500 })
      .withMessage('Message must be between 10 and 500 characters'),
    
    body('severity')
      .isIn(['info', 'success', 'warning', 'error'])
      .withMessage('Severity must be info, success, warning, or error'),
    
    body('relatedId')
      .optional()
      .isLength({ max: 100 })
      .withMessage('Related ID must not exceed 100 characters')
  ],

  // Pagination validation
  pagination: () => [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ],

  // Search validation
  patentSearch: () => [
    query('name')
      .optional()
      .isLength({ min: 2, max: 200 })
      .withMessage('Name search term must be between 2 and 200 characters'),
    
    query('number')
      .optional()
      .matches(/^CN\d{12}\.\d$/)
      .withMessage('Invalid patent number format'),
    
    query('category')
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage('Category must be between 2 and 50 characters'),
    
    query('minPrice')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Minimum price must be a non-negative number'),
    
    query('maxPrice')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Maximum price must be a non-negative number'),
    
    query('status')
      .optional()
      .isIn(['pending', 'approved', 'rejected', 'withdrawn', 'normal'])
      .withMessage('Invalid status value'),
    
    query('sortBy')
      .optional()
      .isIn(['name', 'price', 'uploadDate', 'viewCount'])
      .withMessage('Invalid sortBy field'),
    
    query('sortOrder')
      .optional()
      .isIn(['asc', 'desc'])
      .withMessage('Sort order must be asc or desc')
  ]
};

module.exports = {
  validationRules,
  handleValidationErrors
};
