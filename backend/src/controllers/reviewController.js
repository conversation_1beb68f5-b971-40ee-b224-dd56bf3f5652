const blockchainService = require('../services/blockchainService');
const { formatDate, formatAddress, parsePaginationParams, createPagination } = require('../middleware/responseFormatter');
const { NotFoundError, ValidationError, AuthorizationError } = require('../middleware/errorHandler');

/**
 * Review Controller
 * Handles review operations for patents and transactions
 */

/**
 * Get pending patent uploads for review
 */
const getPendingUploads = async (req, res) => {
  try {
    // Check authorization - only reviewers and admins can view pending uploads
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can view pending uploads');
    }

    console.log('🔍 Getting pending patents for review...');

    // Use the smart contract's getPendingPatents method for efficiency
    const pendingPatentIds = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPendingPatents'
    );

    console.log('🔍 Debug: Pending patent IDs:', pendingPatentIds);

    const pendingUploads = [];

    // Get details for each pending patent
    for (const patentId of pendingPatentIds) {
      try {
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [patentId]
        );

        console.log(`✅ Processing pending patent ${patentId}:`, patent.name);

        // Get uploader profile
        let uploaderProfile = { name: 'Unknown', phone: 'Unknown' };
        try {
          // First check if user is registered
          const isRegistered = await blockchainService.callContractMethod(
            'UserManagement',
            'registeredUsers',
            [patent.uploaderAddress]
          );

          if (isRegistered) {
            uploaderProfile = await blockchainService.callContractMethod(
              'UserManagement',
              'getUserProfile',
              [patent.uploaderAddress]
            );
          }
        } catch (error) {
          console.warn('Failed to get uploader profile:', error);
        }

        // Format patent data for review interface
        pendingUploads.push({
          id: patent.id.toString(),
          patentName: patent.name,
          patentNumber: patent.number,
          category: patent.category,
          price: blockchainService.web3.utils.fromWei(patent.price, 'ether'),
          abstract: patent.abstractText || '',
          applicationDate: formatDate(new Date(parseInt(patent.applicationDate) * 1000)),
          expirationDate: formatDate(new Date(parseInt(patent.expirationDate) * 1000)),
          uploaderAddress: patent.uploaderAddress,
          uploaderAddressShort: formatAddress(patent.uploaderAddress),
          uploaderName: uploaderProfile.name || 'Unknown',
          uploaderPhone: uploaderProfile.phone || 'Unknown',
          ownerName: patent.ownerName,
          ownerIdNumber: patent.ownerIdNumber,
          isAgentSale: patent.isAgentSale,
          submitDate: formatDate(new Date(parseInt(patent.uploadDate) * 1000)),
          documentHashes: {
            patent: patent.documentHash,
            ownership: patent.ownershipDocumentHash
          },
          status: 'pending',
          viewCount: parseInt(patent.viewCount) || 0,
          downloadCount: parseInt(patent.downloadCount) || 0
        });
      } catch (error) {
        console.warn(`Failed to get patent details for ID ${patentId}:`, error);
        continue;
      }
    }

    // Sort by submit date (oldest first for review queue)
    pendingUploads.sort((a, b) => new Date(a.submitDate) - new Date(b.submitDate));

    console.log(`🔍 Debug: Found ${pendingUploads.length} pending uploads`);

    res.success({
      data: pendingUploads,
      total: pendingUploads.length,
      message: `Found ${pendingUploads.length} pending patent uploads`
    });

  } catch (error) {
    console.error('Error getting pending uploads:', error);
    throw error;
  }
};

/**
 * Approve a patent upload
 */
const approveUpload = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, comments } = req.body;
    const patentId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can approve uploads');
    }

    if (isNaN(patentId) || patentId < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    // Verify patent exists
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );

    if (!patent || patent.id.toString() !== id) {
      throw new NotFoundError('Patent not found');
    }

    // Check if patent is pending
    if (parseInt(patent.status) !== 0) { // 0 = pending
      throw new ValidationError('Patent is not pending review');
    }

    // Approve patent on blockchain
    const result = await blockchainService.callContractMethod(
      'PatentRegistry',
      'approvePatent',
      [patentId, comments || 'Patent documentation verified and approved'],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      patentId: id,
      message: '专利上传已批准'
    }, '专利上传已批准');

  } catch (error) {
    console.error('Error approving upload:', error);
    throw error;
  }
};

/**
 * Reject a patent upload
 */
const rejectUpload = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, reason, comments } = req.body;
    const patentId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can reject uploads');
    }

    if (isNaN(patentId) || patentId < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    if (!reason) {
      throw new ValidationError('Rejection reason is required');
    }

    // Verify patent exists
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );

    if (!patent || patent.id.toString() !== id) {
      throw new NotFoundError('Patent not found');
    }

    // Check if patent is pending
    if (parseInt(patent.status) !== 0) { // 0 = pending
      throw new ValidationError('Patent is not pending review');
    }

    // Reject patent on blockchain
    const result = await blockchainService.callContractMethod(
      'PatentRegistry',
      'rejectPatent',
      [patentId, reason],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      patentId: id,
      message: '专利上传已拒绝'
    }, '专利上传已拒绝');

  } catch (error) {
    console.error('Error rejecting upload:', error);
    throw error;
  }
};

/**
 * Get upload details for review
 */
const getUploadDetails = async (req, res) => {
  // Placeholder implementation
  res.success({ message: 'Upload details endpoint - implementation pending' });
};

module.exports = {
  getPendingUploads,
  approveUpload,
  rejectUpload,
  getUploadDetails
};
