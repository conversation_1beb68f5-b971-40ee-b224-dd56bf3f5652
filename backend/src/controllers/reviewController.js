const blockchainService = require('../services/blockchainService');
const { formatDate, formatAddress, parsePaginationParams, createPagination } = require('../middleware/responseFormatter');
const { NotFoundError, ValidationError, AuthorizationError } = require('../middleware/errorHandler');

/**
 * Review Controller
 * Handles review operations for patents and transactions
 */

/**
 * Get pending patent uploads for review
 */
const getPendingUploads = async (req, res) => {
  try {
    // Check authorization - only reviewers and admins can view pending uploads
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can view pending uploads');
    }

    // Get total patent count
    const totalPatents = await blockchainService.callContractMethod(
      'PatentRegistry',
      'totalPatents'
    );

    console.log('🔍 Debug: Total patents:', totalPatents);

    const pendingUploads = [];

    // Get all patents and filter for pending ones
    for (let i = 1; i <= totalPatents; i++) {
      try {
        console.log('🔍 Debug: Getting patent', i);
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [i]
        );

        console.log('🔍 Debug: Patent', i, 'status:', patent.status);

        // Only include pending patents
        if (parseInt(patent.status) === 0) { // 0 = pending
          // Get uploader profile
          let uploaderProfile = { name: 'Unknown', phone: 'Unknown' };
          try {
            // First check if user is registered
            const isRegistered = await blockchainService.callContractMethod(
              'UserManagement',
              'registeredUsers',
              [patent.uploaderAddress]
            );
            
            if (isRegistered) {
              uploaderProfile = await blockchainService.callContractMethod(
                'UserManagement',
                'getUserProfile',
                [patent.uploaderAddress]
              );
            }
          } catch (error) {
            console.warn('Failed to get uploader profile:', error);
          }

          pendingUploads.push({
            id: patent.id.toString(),
            patentName: patent.name,
            patentNumber: patent.number,
            category: patent.category,
            price: blockchainService.web3.utils.fromWei(patent.price, 'ether'),
            uploaderAddress: formatAddress(patent.uploaderAddress),
            uploaderName: uploaderProfile.name || 'Unknown',
            uploaderPhone: uploaderProfile.phone || 'Unknown',
            ownerName: patent.ownerName,
            ownerIdNumber: patent.ownerIdNumber,
            isAgentSale: patent.isAgentSale,
            submitDate: formatDate(new Date(parseInt(patent.uploadDate) * 1000)),
            documentHashes: {
              patent: patent.documentHash,
              ownership: patent.ownershipDocumentHash
            },
            status: 'pending'
          });
        }
      } catch (error) {
        console.warn(`Failed to get patent ${i}:`, error);
        continue;
      }
    }

    // Sort by submit date (oldest first for review queue)
    pendingUploads.sort((a, b) => new Date(a.submitDate) - new Date(b.submitDate));

    res.success({ data: pendingUploads });

  } catch (error) {
    console.error('Error getting pending uploads:', error);
    throw error;
  }
};

/**
 * Approve a patent upload
 */
const approveUpload = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, comments } = req.body;
    const patentId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can approve uploads');
    }

    if (isNaN(patentId) || patentId < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    // Verify patent exists
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );

    if (!patent || patent.id.toString() !== id) {
      throw new NotFoundError('Patent not found');
    }

    // Check if patent is pending
    if (parseInt(patent.status) !== 0) { // 0 = pending
      throw new ValidationError('Patent is not pending review');
    }

    // Approve patent on blockchain
    const result = await blockchainService.callContractMethod(
      'PatentRegistry',
      'approvePatent',
      [patentId, comments || 'Patent documentation verified and approved'],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      patentId: id,
      message: '专利上传已批准'
    }, '专利上传已批准');

  } catch (error) {
    console.error('Error approving upload:', error);
    throw error;
  }
};

/**
 * Reject a patent upload
 */
const rejectUpload = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, reason, comments } = req.body;
    const patentId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can reject uploads');
    }

    if (isNaN(patentId) || patentId < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    if (!reason) {
      throw new ValidationError('Rejection reason is required');
    }

    // Verify patent exists
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );

    if (!patent || patent.id.toString() !== id) {
      throw new NotFoundError('Patent not found');
    }

    // Check if patent is pending
    if (parseInt(patent.status) !== 0) { // 0 = pending
      throw new ValidationError('Patent is not pending review');
    }

    // Reject patent on blockchain
    const result = await blockchainService.callContractMethod(
      'PatentRegistry',
      'rejectPatent',
      [patentId, reason],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      patentId: id,
      message: '专利上传已拒绝'
    }, '专利上传已拒绝');

  } catch (error) {
    console.error('Error rejecting upload:', error);
    throw error;
  }
};

/**
 * Get upload details for review
 */
const getUploadDetails = async (req, res) => {
  // Placeholder implementation
  res.success({ message: 'Upload details endpoint - implementation pending' });
};

module.exports = {
  getPendingUploads,
  approveUpload,
  rejectUpload,
  getUploadDetails
};
