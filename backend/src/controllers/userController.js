const blockchainService = require('../services/blockchainService');
const { formatDate, formatAddress } = require('../middleware/responseFormatter');
const { AuthorizationError, NotFoundError, ConflictError } = require('../middleware/errorHandler');

/**
 * User Controller
 * Handles user management operations
 */

/**
 * Get user role by blockchain address
 */
const getUserRole = async (req, res) => {
  const { address } = req.params;

  try {
    // Get role from blockchain
    const role = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserRole',
      [address]
    );

    // Get user profile if exists
    let userProfile = null;
    try {
      const profile = await blockchainService.callContractMethod(
        'UserManagement',
        'getUserProfile',
        [address]
      );
      userProfile = profile;
    } catch (error) {
      // User might not be registered yet
    }

    // Convert role number to string
    const roleMap = {
      0: 'user',
      1: 'reviewer', 
      2: 'admin'
    };

    const roleString = roleMap[role] || 'user';

    // Define permissions based on role
    const permissionsMap = {
      user: ['upload', 'search', 'trade', 'profile'],
      reviewer: ['upload', 'search', 'trade', 'profile', 'review', 'approve', 'reject'],
      admin: ['upload', 'search', 'trade', 'profile', 'review', 'approve', 'reject', 'manage_users', 'statistics']
    };

    const response = {
      address: formatAddress(address),
      role: roleString,
      permissions: permissionsMap[roleString],
      lastUpdated: userProfile ? formatDate(userProfile.registrationDate) : null
    };

    res.success(response, 'User role retrieved successfully');
  } catch (error) {
    console.error('Error getting user role:', error);
    throw error;
  }
};

/**
 * Update user role (admin only)
 */
const updateUserRole = async (req, res) => {
  const { address, role, updatedBy } = req.body;

  try {
    // Verify the updater is admin
    if (req.userAddress !== updatedBy) {
      throw new AuthorizationError('UpdatedBy address must match authenticated user');
    }

    // Convert role string to number
    const roleMap = {
      'user': 0,
      'reviewer': 1,
      'admin': 2
    };

    const roleNumber = roleMap[role];
    if (roleNumber === undefined) {
      throw new ValidationError('Invalid role specified');
    }

    // Update role in blockchain
    const tx = await blockchainService.callContractMethod(
      'UserManagement',
      'changeUserRole',
      [address, roleNumber],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: tx.transactionHash,
      address: formatAddress(address),
      newRole: role,
      updatedBy: formatAddress(updatedBy)
    }, 'User role updated successfully');
  } catch (error) {
    console.error('Error updating user role:', error);
    throw error;
  }
};

/**
 * Get user profile information
 */
const getUserProfile = async (req, res) => {
  const { address } = req.params;

  try {
    // Check if user is registered
    const isRegistered = await blockchainService.callContractMethod(
      'UserManagement',
      'registeredUsers',
      [address]
    );

    if (!isRegistered) {
      throw new NotFoundError('User not found');
    }

    // Get user profile from blockchain
    const profile = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserProfile',
      [address]
    );

    // Get user role
    const role = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserRole',
      [address]
    );

    const roleMap = {
      0: 'user',
      1: 'reviewer', 
      2: 'admin'
    };

    const response = {
      address: formatAddress(address),
      name: profile.name,
      phone: profile.phone,
      idNumber: profile.idNumber,
      role: roleMap[role] || 'user',
      registrationDate: formatDate(profile.registrationDate),
      lastLoginDate: formatDate(profile.lastLoginDate),
      status: profile.isActive ? 'active' : 'inactive'
    };

    res.success(response, 'User profile retrieved successfully');
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

/**
 * Update user profile
 */
const updateUserProfile = async (req, res) => {
  const { address } = req.params;
  const { name, phone, idNumber } = req.body;

  try {
    // Check authorization - user can update own profile or admin can update any
    if (req.userRole !== 'admin' && req.userAddress !== address) {
      throw new AuthorizationError('You can only update your own profile');
    }

    // Update profile in blockchain
    const tx = await blockchainService.callContractMethod(
      'UserManagement',
      'updateUserProfile',
      [address, name, phone, idNumber],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: tx.transactionHash,
      address: formatAddress(address),
      updatedFields: { name, phone, idNumber }
    }, 'User profile updated successfully');
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

/**
 * Register a new user
 */
const registerUser = async (req, res) => {
  const { name, phone, idNumber } = req.body;
  const userAddress = req.userAddress;

  try {
    // Check if user is already registered
    const isRegistered = await blockchainService.callContractMethod(
      'UserManagement',
      'registeredUsers',
      [userAddress]
    );

    if (isRegistered) {
      throw new ConflictError('User is already registered');
    }

    // Register user in blockchain
    const tx = await blockchainService.callContractMethod(
      'UserManagement',
      'registerUser',
      [userAddress, name, phone, idNumber],
      { send: true, from: userAddress }
    );

    res.created({
      transactionHash: tx.transactionHash,
      address: formatAddress(userAddress),
      name,
      role: 'user'
    }, 'User registered successfully');
  } catch (error) {
    console.error('Error registering user:', error);
    throw error;
  }
};

/**
 * Update last login time
 */
const updateLastLogin = async (req, res) => {
  const userAddress = req.userAddress;

  try {
    // Update last login time in blockchain
    const tx = await blockchainService.callContractMethod(
      'UserManagement',
      'updateLastLogin',
      [userAddress],
      { send: true, from: userAddress }
    );

    res.success({
      transactionHash: tx.transactionHash,
      address: formatAddress(userAddress),
      loginTime: new Date().toISOString()
    }, 'Last login time updated successfully');
  } catch (error) {
    console.error('Error updating last login:', error);
    throw error;
  }
};

/**
 * Get user permissions
 */
const getUserPermissions = async (req, res) => {
  const { address } = req.params;

  try {
    // Get user role
    const role = await blockchainService.callContractMethod(
      'UserManagement',
      'getUserRole',
      [address]
    );

    // Check if user is active
    const isActive = await blockchainService.callContractMethod(
      'UserManagement',
      'isUserActive',
      [address]
    );

    const roleMap = {
      0: 'user',
      1: 'reviewer', 
      2: 'admin'
    };

    const roleString = roleMap[role] || 'user';

    // Define permissions based on role
    const permissionsMap = {
      user: ['upload', 'search', 'trade', 'profile'],
      reviewer: ['upload', 'search', 'trade', 'profile', 'review', 'approve', 'reject'],
      admin: ['upload', 'search', 'trade', 'profile', 'review', 'approve', 'reject', 'manage_users', 'statistics']
    };

    const response = {
      address: formatAddress(address),
      role: roleString,
      isActive,
      permissions: isActive ? permissionsMap[roleString] : []
    };

    res.success(response, 'User permissions retrieved successfully');
  } catch (error) {
    console.error('Error getting user permissions:', error);
    throw error;
  }
};

/**
 * Update user status (activate/deactivate)
 */
const updateUserStatus = async (req, res) => {
  const { address } = req.params;
  const { isActive } = req.body;

  try {
    // Update user status in blockchain
    const tx = await blockchainService.callContractMethod(
      'UserManagement',
      'setUserStatus',
      [address, isActive],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: tx.transactionHash,
      address: formatAddress(address),
      status: isActive ? 'active' : 'inactive'
    }, `User ${isActive ? 'activated' : 'deactivated'} successfully`);
  } catch (error) {
    console.error('Error updating user status:', error);
    throw error;
  }
};

module.exports = {
  getUserRole,
  updateUserRole,
  getUserProfile,
  updateUserProfile,
  registerUser,
  updateLastLogin,
  getUserPermissions,
  updateUserStatus
};
