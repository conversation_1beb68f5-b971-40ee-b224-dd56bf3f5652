const blockchainService = require('../services/blockchainService');
const { formatDate, formatAddress, parsePaginationParams, createPagination } = require('../middleware/responseFormatter');
const { NotFoundError, ValidationError, AuthorizationError } = require('../middleware/errorHandler');

/**
 * Transaction Controller
 * Handles transaction management operations
 */

/**
 * Initiate a patent purchase transaction
 */
const initiateTransaction = async (req, res) => {
  try {
    const { patentId, sellerAddress, price } = req.body;
    const buyerAddress = req.userAddress;

    // Validate inputs
    if (!patentId || !sellerAddress || !price) {
      throw new ValidationError('Patent ID, seller address, and price are required');
    }

    if (!blockchainService.web3.utils.isAddress(sellerAddress)) {
      throw new ValidationError('Invalid seller address format');
    }

    if (buyerAddress.toLowerCase() === sellerAddress.toLowerCase()) {
      throw new ValidationError('Cannot purchase your own patent');
    }

    const patentIdNum = parseInt(patentId);
    if (isNaN(patentIdNum) || patentIdNum < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    // Verify patent exists and is available for trading
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentIdNum]
    );

    if (!patent || patent.id.toString() !== patentId.toString()) {
      throw new NotFoundError('Patent not found');
    }

    // Check if patent is available for trading
    if (patent.status !== 1 && patent.status !== 3) { // 1=approved, 3=normal
      throw new ValidationError('Patent is not available for trading');
    }

    // Verify seller is the patent owner
    if (patent.uploaderAddress.toLowerCase() !== sellerAddress.toLowerCase()) {
      throw new ValidationError('Seller is not the patent owner');
    }

    // Convert price to Wei
    const priceInWei = blockchainService.web3.utils.toWei(price.toString(), 'ether');

    // Initiate transaction on blockchain
    const result = await blockchainService.callContractMethod(
      'TransactionManager',
      'initiateTransaction',
      [patentIdNum, sellerAddress, priceInWei],
      { send: true, from: buyerAddress }
    );

    // Extract transaction ID from events
    let transactionId = null;
    if (result.events && result.events.TransactionInitiated) {
      transactionId = result.events.TransactionInitiated.returnValues.transactionId;
    }

    res.created({
      transactionId: transactionId || 'pending',
      status: 'pending',
      transactionHash: result.transactionHash,
      message: '交易已发起，等待审核'
    }, '交易已发起，等待审核');

  } catch (error) {
    console.error('Error initiating transaction:', error);
    throw error;
  }
};

/**
 * Get user's transaction history
 */
const getUserTransactions = async (req, res) => {
  try {
    const { address } = req.params;
    const { type, status, page = 1, limit = 20 } = req.query;

    // Validate address
    if (!blockchainService.web3.utils.isAddress(address)) {
      throw new ValidationError('Invalid address format');
    }

    // Check authorization
    if (req.userRole === 'user' && req.userAddress.toLowerCase() !== address.toLowerCase()) {
      throw new AuthorizationError('You can only view your own transactions');
    }

    const { offset, pageSize } = parsePaginationParams(page, limit);

    // Get user's transactions from blockchain
    const buyerTransactions = await blockchainService.callContractMethod(
      'TransactionManager',
      'getBuyerTransactions',
      [address]
    );

    const sellerTransactions = await blockchainService.callContractMethod(
      'TransactionManager',
      'getSellerTransactions',
      [address]
    );

    const allTransactionIds = [...buyerTransactions, ...sellerTransactions];
    const transactions = [];

    // Get details for each transaction
    for (const txId of allTransactionIds) {
      try {
        const transaction = await blockchainService.callContractMethod(
          'TransactionManager',
          'getTransaction',
          [txId]
        );

        // Get patent details
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [transaction.patentId]
        );

        // Get user profiles
        let buyerName = 'Unknown';
        let sellerName = 'Unknown';
        try {
          const buyerProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [transaction.buyerAddress]
          );
          buyerName = buyerProfile.name || 'Unknown';
        } catch (error) {
          console.warn('Failed to get buyer profile:', error);
        }

        try {
          const sellerProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [transaction.sellerAddress]
          );
          sellerName = sellerProfile.name || 'Unknown';
        } catch (error) {
          console.warn('Failed to get seller profile:', error);
        }

        const statusNames = ['pending', 'approved', 'rejected', 'completed', 'cancelled'];
        const userType = transaction.buyerAddress.toLowerCase() === address.toLowerCase() ? 'purchase' : 'sale';

        // Apply filters
        if (type && type !== userType) continue;
        if (status && statusNames[transaction.status] !== status) continue;

        transactions.push({
          id: transaction.id.toString(),
          patentId: transaction.patentId.toString(),
          patentName: patent.name,
          patentNumber: patent.number,
          buyerAddress: formatAddress(transaction.buyerAddress),
          buyerName,
          sellerAddress: formatAddress(transaction.sellerAddress),
          sellerName,
          price: blockchainService.web3.utils.fromWei(transaction.price, 'ether'),
          submitDate: formatDate(new Date(parseInt(transaction.submitDate) * 1000)),
          status: statusNames[transaction.status] || 'unknown',
          type: userType
        });
      } catch (error) {
        console.warn(`Failed to get transaction details for ID ${txId}:`, error);
        continue;
      }
    }

    // Sort by submit date (newest first)
    transactions.sort((a, b) => new Date(b.submitDate) - new Date(a.submitDate));

    // Apply pagination
    const paginatedTransactions = transactions.slice(offset, offset + pageSize);
    const pagination = createPagination(page, pageSize, transactions.length);

    res.success({
      transactions: paginatedTransactions,
      pagination
    });

  } catch (error) {
    console.error('Error getting user transactions:', error);
    throw error;
  }
};

/**
 * Get pending transactions for review
 */
const getPendingTransactions = async (req, res) => {
  try {
    // Check authorization - only reviewers and admins can view pending transactions
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can view pending transactions');
    }

    // Get pending transactions from blockchain
    const pendingTransactions = await blockchainService.callContractMethod(
      'TransactionManager',
      'getPendingTransactions'
    );

    const transactions = [];

    // Get details for each pending transaction
    for (const txId of pendingTransactions) {
      try {
        const transaction = await blockchainService.callContractMethod(
          'TransactionManager',
          'getTransaction',
          [txId]
        );

        // Get patent details
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [transaction.patentId]
        );

        // Get user profiles
        let buyerName = 'Unknown';
        let sellerName = 'Unknown';
        try {
          const buyerProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [transaction.buyerAddress]
          );
          buyerName = buyerProfile.name || 'Unknown';
        } catch (error) {
          console.warn('Failed to get buyer profile:', error);
        }

        try {
          const sellerProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [transaction.sellerAddress]
          );
          sellerName = sellerProfile.name || 'Unknown';
        } catch (error) {
          console.warn('Failed to get seller profile:', error);
        }

        transactions.push({
          id: transaction.id.toString(),
          patentId: transaction.patentId.toString(),
          patentName: patent.name,
          patentNumber: patent.number,
          buyerAddress: formatAddress(transaction.buyerAddress),
          buyerName,
          sellerAddress: formatAddress(transaction.sellerAddress),
          sellerName,
          price: blockchainService.web3.utils.fromWei(transaction.price, 'ether'),
          submitDate: formatDate(new Date(parseInt(transaction.submitDate) * 1000)),
          status: 'pending',
          type: 'purchase'
        });
      } catch (error) {
        console.warn(`Failed to get transaction details for ID ${txId}:`, error);
        continue;
      }
    }

    // Sort by submit date (oldest first for review queue)
    transactions.sort((a, b) => new Date(a.submitDate) - new Date(b.submitDate));

    res.success({ data: transactions });

  } catch (error) {
    console.error('Error getting pending transactions:', error);
    throw error;
  }
};

/**
 * Approve a transaction
 */
const approveTransaction = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, comments } = req.body;
    const transactionId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can approve transactions');
    }

    if (isNaN(transactionId) || transactionId < 0) {
      throw new ValidationError('Invalid transaction ID');
    }

    // Verify transaction exists
    const transaction = await blockchainService.callContractMethod(
      'TransactionManager',
      'getTransaction',
      [transactionId]
    );

    if (!transaction || transaction.id.toString() !== id) {
      throw new NotFoundError('Transaction not found');
    }

    // Check if transaction is pending
    if (transaction.status !== 0) { // 0 = pending
      throw new ValidationError('Transaction is not pending review');
    }

    // Approve transaction on blockchain
    const result = await blockchainService.callContractMethod(
      'TransactionManager',
      'approveTransaction',
      [transactionId, comments || 'Transaction approved after verification'],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      transactionId: id,
      message: '交易已批准'
    }, '交易已批准');

  } catch (error) {
    console.error('Error approving transaction:', error);
    throw error;
  }
};

/**
 * Reject a transaction
 */
const rejectTransaction = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, reason, comments } = req.body;
    const transactionId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can reject transactions');
    }

    if (isNaN(transactionId) || transactionId < 0) {
      throw new ValidationError('Invalid transaction ID');
    }

    if (!reason) {
      throw new ValidationError('Rejection reason is required');
    }

    // Verify transaction exists
    const transaction = await blockchainService.callContractMethod(
      'TransactionManager',
      'getTransaction',
      [transactionId]
    );

    if (!transaction || transaction.id.toString() !== id) {
      throw new NotFoundError('Transaction not found');
    }

    // Check if transaction is pending
    if (transaction.status !== 0) { // 0 = pending
      throw new ValidationError('Transaction is not pending review');
    }

    // Reject transaction on blockchain
    const result = await blockchainService.callContractMethod(
      'TransactionManager',
      'rejectTransaction',
      [transactionId, reason],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      transactionId: id,
      message: '交易已拒绝'
    }, '交易已拒绝');

  } catch (error) {
    console.error('Error rejecting transaction:', error);
    throw error;
  }
};

/**
 * Get transaction details
 */
const getTransactionDetails = async (req, res) => {
  // Placeholder implementation
  res.success({ message: 'Transaction details endpoint - implementation pending' });
};

/**
 * Cancel a transaction
 */
const cancelTransaction = async (req, res) => {
  // Placeholder implementation
  res.success({ message: 'Transaction cancel endpoint - implementation pending' });
};

module.exports = {
  initiateTransaction,
  getUserTransactions,
  getPendingTransactions,
  approveTransaction,
  rejectTransaction,
  getTransactionDetails,
  cancelTransaction
};
