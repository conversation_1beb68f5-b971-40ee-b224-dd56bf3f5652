const express = require('express');
const router = express.Router();

// Import middleware
const { 
  extractUserAddress, 
  verifyActiveUser, 
  getUserRole, 
  requireAdmin
} = require('../middleware/authMiddleware');
const { validationRules, handleValidationErrors } = require('../middleware/validationMiddleware');
const { asyncHandler } = require('../middleware/errorHandler');

// Import controllers
const adminController = require('../controllers/adminController');

/**
 * @route GET /api/admin/users
 * @desc Get all users (admin only)
 * @access Admin
 */
router.get('/users',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireAdmin,
  validationRules.pagination(),
  handleValidationErrors,
  asyncHandler(adminController.getAllUsers)
);

/**
 * @route GET /api/admin/users/statistics
 * @desc Get user statistics (admin only)
 * @access Admin
 */
router.get('/users/statistics',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireAdmin,
  async<PERSON><PERSON><PERSON>(adminController.getUserStatistics)
);

/**
 * @route GET /api/admin/statistics/overview
 * @desc Get system overview statistics (admin only)
 * @access Admin
 */
router.get('/statistics/overview',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireAdmin,
  asyncHandler(adminController.getSystemOverview)
);

/**
 * @route GET /api/admin/statistics/patents
 * @desc Get detailed patent statistics (admin only)
 * @access Admin
 */
router.get('/statistics/patents',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireAdmin,
  asyncHandler(adminController.getPatentStatistics)
);

/**
 * @route GET /api/admin/statistics/transactions
 * @desc Get detailed transaction statistics (admin only)
 * @access Admin
 */
router.get('/statistics/transactions',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireAdmin,
  asyncHandler(adminController.getTransactionStatistics)
);

module.exports = router;
