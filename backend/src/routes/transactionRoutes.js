const express = require('express');
const router = express.Router();

// Import middleware
const { 
  extractUserAddress, 
  verifyActiveUser, 
  getUserRole, 
  requireReviewerOrAdmin,
  validateAddressParam
} = require('../middleware/authMiddleware');
const { validationRules, handleValidationErrors } = require('../middleware/validationMiddleware');
const { asyncHandler } = require('../middleware/errorHandler');

// Import controllers
const transactionController = require('../controllers/transactionController');

/**
 * @route POST /api/transactions/initiate
 * @desc Initiate a patent purchase transaction
 * @access Authenticated users
 */
router.post('/initiate',
  extractUserAddress,
  verifyActiveUser,
  validationRules.transactionInitiate(),
  handleValidationErrors,
  asyncHandler(transactionController.initiateTransaction)
);

/**
 * @route GET /api/transactions/user/:address
 * @desc Get user's transaction history
 * @access User (own transactions) or Admin
 */
router.get('/user/:address',
  validateAddressParam('address'),
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  validationRules.pagination(),
  handleValidationErrors,
  asyncHandler(transactionController.getUserTransactions)
);

/**
 * @route GET /api/transactions/pending
 * @desc Get pending transactions for review (reviewer/admin only)
 * @access Reviewer/Admin
 */
router.get('/pending',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  validationRules.pagination(),
  handleValidationErrors,
  asyncHandler(transactionController.getPendingTransactions)
);

/**
 * @route PUT /api/transactions/:id/approve
 * @desc Approve a transaction (reviewer/admin only)
 * @access Reviewer/Admin
 */
router.put('/:id/approve',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  validationRules.reviewAction(),
  handleValidationErrors,
  asyncHandler(transactionController.approveTransaction)
);

/**
 * @route PUT /api/transactions/:id/reject
 * @desc Reject a transaction (reviewer/admin only)
 * @access Reviewer/Admin
 */
router.put('/:id/reject',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  validationRules.reviewReject(),
  handleValidationErrors,
  asyncHandler(transactionController.rejectTransaction)
);

/**
 * @route GET /api/transactions/:id
 * @desc Get transaction details
 * @access Transaction parties or Admin
 */
router.get('/:id',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  asyncHandler(transactionController.getTransactionDetails)
);

/**
 * @route PUT /api/transactions/:id/cancel
 * @desc Cancel a transaction
 * @access Transaction parties or Admin
 */
router.put('/:id/cancel',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  asyncHandler(transactionController.cancelTransaction)
);

module.exports = router;
