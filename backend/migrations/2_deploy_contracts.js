const UserManagement = artifacts.require("UserManagement");
const PatentRegistry = artifacts.require("PatentRegistry");
const TransactionManager = artifacts.require("TransactionManager");
const ProtectionManager = artifacts.require("ProtectionManager");
const NotificationSystem = artifacts.require("NotificationSystem");

module.exports = async function (deployer, network, accounts) {
  console.log("Deploying contracts to network:", network);
  console.log("Using account:", accounts[0]);

  try {
    // Deploy UserManagement first (no dependencies)
    console.log("Deploying UserManagement...");
    await deployer.deploy(UserManagement);
    const userManagement = await UserManagement.deployed();
    console.log("UserManagement deployed at:", userManagement.address);

    // Deploy NotificationSystem (depends on UserManagement)
    console.log("Deploying NotificationSystem...");
    await deployer.deploy(NotificationSystem, userManagement.address);
    const notificationSystem = await NotificationSystem.deployed();
    console.log("NotificationSystem deployed at:", notificationSystem.address);

    // Deploy PatentRegistry (depends on UserManagement)
    console.log("Deploying PatentRegistry...");
    await deployer.deploy(PatentRegistry, userManagement.address);
    const patentRegistry = await PatentRegistry.deployed();
    console.log("PatentRegistry deployed at:", patentRegistry.address);

    // Deploy TransactionManager (depends on UserManagement and PatentRegistry)
    console.log("Deploying TransactionManager...");
    await deployer.deploy(TransactionManager, userManagement.address, patentRegistry.address);
    const transactionManager = await TransactionManager.deployed();
    console.log("TransactionManager deployed at:", transactionManager.address);

    // Deploy ProtectionManager (depends on UserManagement and PatentRegistry)
    console.log("Deploying ProtectionManager...");
    await deployer.deploy(ProtectionManager, userManagement.address, patentRegistry.address);
    const protectionManager = await ProtectionManager.deployed();
    console.log("ProtectionManager deployed at:", protectionManager.address);

    console.log("\n=== Deployment Summary ===");
    console.log("UserManagement:", userManagement.address);
    console.log("NotificationSystem:", notificationSystem.address);
    console.log("PatentRegistry:", patentRegistry.address);
    console.log("TransactionManager:", transactionManager.address);
    console.log("ProtectionManager:", protectionManager.address);

    // Save contract addresses to a file for the backend to use
    const fs = require('fs');
    const contractAddresses = {
      UserManagement: userManagement.address,
      NotificationSystem: notificationSystem.address,
      PatentRegistry: patentRegistry.address,
      TransactionManager: transactionManager.address,
      ProtectionManager: protectionManager.address,
      network: network,
      deployedAt: new Date().toISOString()
    };

    fs.writeFileSync(
      './contract-addresses.json',
      JSON.stringify(contractAddresses, null, 2)
    );
    console.log("\nContract addresses saved to contract-addresses.json");

  } catch (error) {
    console.error("Deployment failed:", error);
    throw error;
  }
};
