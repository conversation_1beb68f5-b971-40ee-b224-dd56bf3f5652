// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "./UserManagement.sol";
import "./PatentRegistry.sol";

/**
 * @title ProtectionManager
 * @dev Smart contract for managing patent rights protection cases
 */
contract ProtectionManager {
    
    // Protection case status enum
    enum ProtectionStatus { PENDING, APPROVED, REJECTED, RESOLVED }
    
    // Protection case structure
    struct ProtectionCase {
        uint256 id;
        uint256 patentId;
        address claimantAddress;
        address currentOwnerAddress;
        string description;
        string evidenceHash; // IPFS hash for evidence files
        ProtectionStatus status;
        uint256 submitDate;
        address reviewedBy;
        string resolution;
        uint256 reviewDate;
        string blockchainTxHash;
    }
    
    // Events
    event ProtectionRequested(
        uint256 indexed caseId, 
        uint256 indexed patentId, 
        address indexed claimant, 
        address currentOwner
    );
    event ProtectionApproved(uint256 indexed caseId, address indexed reviewer, string resolution);
    event ProtectionRejected(uint256 indexed caseId, address indexed reviewer, string reason);
    event ProtectionResolved(uint256 indexed caseId, string blockchainTxHash);
    
    // State variables
    mapping(uint256 => ProtectionCase) public protectionCases;
    mapping(address => uint256[]) public userProtectionCases; // Cases initiated by user
    mapping(uint256 => uint256[]) public patentProtectionCases; // Cases for a patent
    uint256 public nextCaseId;
    uint256 public totalCases;
    
    UserManagement public userManagement;
    PatentRegistry public patentRegistry;
    
    // Modifiers
    modifier onlyReviewerOrAdmin() {
        UserManagement.Role role = userManagement.getUserRole(msg.sender);
        require(
            role == UserManagement.Role.REVIEWER || 
            role == UserManagement.Role.ADMIN,
            "Only reviewer or admin can perform this action"
        );
        _;
    }
    
    modifier caseExists(uint256 caseId) {
        require(
            caseId < nextCaseId && 
            protectionCases[caseId].id == caseId, 
            "Protection case does not exist"
        );
        _;
    }
    
    modifier onlyClaimant(uint256 caseId) {
        require(
            protectionCases[caseId].claimantAddress == msg.sender,
            "Only claimant can perform this action"
        );
        _;
    }
    
    constructor(address _userManagementAddress, address _patentRegistryAddress) {
        userManagement = UserManagement(_userManagementAddress);
        patentRegistry = PatentRegistry(_patentRegistryAddress);
        nextCaseId = 1;
        totalCases = 0;
    }
    
    /**
     * @dev Submit a rights protection request
     */
    function submitProtectionRequest(
        uint256 patentId,
        address currentOwnerAddress,
        string memory description,
        string memory evidenceHash
    ) external returns (uint256) {
        // Verify patent exists
        PatentRegistry.Patent memory patent = patentRegistry.getPatent(patentId);
        require(patent.id == patentId, "Patent does not exist");
        require(patent.uploaderAddress == currentOwnerAddress, "Invalid current owner address");
        require(msg.sender != currentOwnerAddress, "Cannot file protection against yourself");
        require(bytes(description).length > 0, "Description cannot be empty");
        require(bytes(evidenceHash).length > 0, "Evidence hash cannot be empty");
        require(userManagement.isUserActive(msg.sender), "Claimant is not active");
        
        uint256 caseId = nextCaseId;
        nextCaseId++;
        
        protectionCases[caseId] = ProtectionCase({
            id: caseId,
            patentId: patentId,
            claimantAddress: msg.sender,
            currentOwnerAddress: currentOwnerAddress,
            description: description,
            evidenceHash: evidenceHash,
            status: ProtectionStatus.PENDING,
            submitDate: block.timestamp,
            reviewedBy: address(0),
            resolution: "",
            reviewDate: 0,
            blockchainTxHash: ""
        });
        
        userProtectionCases[msg.sender].push(caseId);
        patentProtectionCases[patentId].push(caseId);
        totalCases++;
        
        emit ProtectionRequested(caseId, patentId, msg.sender, currentOwnerAddress);
        
        return caseId;
    }
    
    /**
     * @dev Approve a protection request (reviewer/admin only)
     */
    function approveProtection(uint256 caseId, string memory resolution) 
        external 
        onlyReviewerOrAdmin 
        caseExists(caseId) 
    {
        require(
            protectionCases[caseId].status == ProtectionStatus.PENDING, 
            "Case is not pending review"
        );
        require(bytes(resolution).length > 0, "Resolution cannot be empty");
        
        protectionCases[caseId].status = ProtectionStatus.APPROVED;
        protectionCases[caseId].reviewedBy = msg.sender;
        protectionCases[caseId].resolution = resolution;
        protectionCases[caseId].reviewDate = block.timestamp;
        
        emit ProtectionApproved(caseId, msg.sender, resolution);
    }
    
    /**
     * @dev Reject a protection request (reviewer/admin only)
     */
    function rejectProtection(uint256 caseId, string memory reason) 
        external 
        onlyReviewerOrAdmin 
        caseExists(caseId) 
    {
        require(
            protectionCases[caseId].status == ProtectionStatus.PENDING, 
            "Case is not pending review"
        );
        require(bytes(reason).length > 0, "Reason cannot be empty");
        
        protectionCases[caseId].status = ProtectionStatus.REJECTED;
        protectionCases[caseId].reviewedBy = msg.sender;
        protectionCases[caseId].resolution = reason;
        protectionCases[caseId].reviewDate = block.timestamp;
        
        emit ProtectionRejected(caseId, msg.sender, reason);
    }
    
    /**
     * @dev Mark protection case as resolved (after blockchain action)
     */
    function resolveProtection(uint256 caseId, string memory blockchainTxHash) 
        external 
        onlyReviewerOrAdmin 
        caseExists(caseId) 
    {
        require(
            protectionCases[caseId].status == ProtectionStatus.APPROVED, 
            "Case is not approved"
        );
        require(bytes(blockchainTxHash).length > 0, "Blockchain transaction hash required");
        
        protectionCases[caseId].status = ProtectionStatus.RESOLVED;
        protectionCases[caseId].blockchainTxHash = blockchainTxHash;
        
        emit ProtectionResolved(caseId, blockchainTxHash);
    }
    
    /**
     * @dev Get protection case details
     */
    function getProtectionCase(uint256 caseId) 
        external 
        view 
        caseExists(caseId) 
        returns (ProtectionCase memory) 
    {
        return protectionCases[caseId];
    }
    
    /**
     * @dev Get user's protection cases
     */
    function getUserProtectionCases(address userAddress) external view returns (uint256[] memory) {
        return userProtectionCases[userAddress];
    }
    
    /**
     * @dev Get patent's protection cases
     */
    function getPatentProtectionCases(uint256 patentId) external view returns (uint256[] memory) {
        return patentProtectionCases[patentId];
    }
    
    /**
     * @dev Get pending protection cases for review
     */
    function getPendingProtectionCases() external view onlyReviewerOrAdmin returns (uint256[] memory) {
        uint256[] memory pendingCases = new uint256[](totalCases);
        uint256 count = 0;
        
        for (uint256 i = 1; i < nextCaseId; i++) {
            if (protectionCases[i].status == ProtectionStatus.PENDING) {
                pendingCases[count] = i;
                count++;
            }
        }
        
        // Resize array to actual count
        uint256[] memory result = new uint256[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = pendingCases[i];
        }
        
        return result;
    }
    
    /**
     * @dev Get total protection cases count
     */
    function getTotalProtectionCases() external view returns (uint256) {
        return totalCases;
    }
    
    /**
     * @dev Get protection cases by status
     */
    function getProtectionCasesByStatus(ProtectionStatus status) 
        external 
        view 
        onlyReviewerOrAdmin 
        returns (uint256[] memory) 
    {
        uint256[] memory statusCases = new uint256[](totalCases);
        uint256 count = 0;
        
        for (uint256 i = 1; i < nextCaseId; i++) {
            if (protectionCases[i].status == status) {
                statusCases[count] = i;
                count++;
            }
        }
        
        // Resize array to actual count
        uint256[] memory result = new uint256[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = statusCases[i];
        }
        
        return result;
    }
    
    /**
     * @dev Check if patent has pending protection cases
     */
    function hasPatentPendingProtection(uint256 patentId) external view returns (bool) {
        uint256[] memory cases = patentProtectionCases[patentId];
        for (uint256 i = 0; i < cases.length; i++) {
            if (protectionCases[cases[i]].status == ProtectionStatus.PENDING) {
                return true;
            }
        }
        return false;
    }
}
