// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "./UserManagement.sol";

/**
 * @title PatentRegistry
 * @dev Smart contract for managing patent registration and metadata
 */
contract PatentRegistry {
    
    // Patent status enum
    enum PatentStatus { PENDING, APPROVED, REJECTED, WITHDRAWN, NORMAL }
    
    // Patent structure
    struct Patent {
        uint256 id;
        string name;
        string number;
        string category;
        uint256 price;
        string abstractText;
        uint256 applicationDate;
        uint256 expirationDate;
        string ownerName;
        string ownerIdNumber;
        address uploaderAddress;
        bool isAgentSale;
        PatentStatus status;
        string documentHash; // IPFS hash for patent document
        string ownershipDocumentHash; // IPFS hash for ownership document
        uint256 uploadDate;
        uint256 viewCount;
        uint256 downloadCount;
        string blockchainTxHash;
        uint256 blockNumber;
        address reviewedBy;
        string reviewComments;
        uint256 reviewDate;
    }
    
    // Events
    event PatentUploaded(uint256 indexed patentId, address indexed uploader, string name);
    event PatentApproved(uint256 indexed patentId, address indexed reviewer);
    event PatentRejected(uint256 indexed patentId, address indexed reviewer, string reason);
    event PatentWithdrawn(uint256 indexed patentId, address indexed owner, string reason);
    event PatentRestored(uint256 indexed patentId, address indexed owner);
    event PatentViewed(uint256 indexed patentId, address indexed viewer);
    event PatentDownloaded(uint256 indexed patentId, address indexed downloader);
    
    // State variables
    mapping(uint256 => Patent) public patents;
    mapping(address => uint256[]) public userPatents;
    mapping(string => bool) public patentNumbers; // To ensure unique patent numbers
    uint256 public nextPatentId;
    uint256 public totalPatents;
    
    UserManagement public userManagement;
    
    // Modifiers
    modifier onlyReviewerOrAdmin() {
        UserManagement.Role role = userManagement.getUserRole(msg.sender);
        require(
            role == UserManagement.Role.REVIEWER || 
            role == UserManagement.Role.ADMIN,
            "Only reviewer or admin can perform this action"
        );
        _;
    }
    
    modifier patentExists(uint256 patentId) {
        require(patentId < nextPatentId && patents[patentId].id == patentId, "Patent does not exist");
        _;
    }
    
    modifier onlyPatentOwner(uint256 patentId) {
        require(patents[patentId].uploaderAddress == msg.sender, "Only patent owner can perform this action");
        _;
    }
    
    constructor(address _userManagementAddress) {
        userManagement = UserManagement(_userManagementAddress);
        nextPatentId = 1;
        totalPatents = 0;
    }
    
    /**
     * @dev Upload a new patent for review
     */
    function uploadPatent(
        string memory name,
        string memory number,
        string memory category,
        uint256 price,
        string memory abstractText,
        uint256 applicationDate,
        uint256 expirationDate,
        string memory ownerName,
        string memory ownerIdNumber,
        bool isAgentSale,
        string memory documentHash,
        string memory ownershipDocumentHash
    ) external returns (uint256) {
        require(bytes(name).length > 0, "Patent name cannot be empty");
        require(bytes(number).length > 0, "Patent number cannot be empty");
        require(!patentNumbers[number], "Patent number already exists");
        require(price > 0, "Price must be greater than 0");
        require(applicationDate < expirationDate, "Invalid date range");
        require(bytes(documentHash).length > 0, "Document hash cannot be empty");
        require(userManagement.isUserActive(msg.sender), "User is not active");
        
        uint256 patentId = nextPatentId;
        nextPatentId++;
        
        patents[patentId] = Patent({
            id: patentId,
            name: name,
            number: number,
            category: category,
            price: price,
            abstractText: abstractText,
            applicationDate: applicationDate,
            expirationDate: expirationDate,
            ownerName: ownerName,
            ownerIdNumber: ownerIdNumber,
            uploaderAddress: msg.sender,
            isAgentSale: isAgentSale,
            status: PatentStatus.PENDING,
            documentHash: documentHash,
            ownershipDocumentHash: ownershipDocumentHash,
            uploadDate: block.timestamp,
            viewCount: 0,
            downloadCount: 0,
            blockchainTxHash: "",
            blockNumber: block.number,
            reviewedBy: address(0),
            reviewComments: "",
            reviewDate: 0
        });
        
        userPatents[msg.sender].push(patentId);
        patentNumbers[number] = true;
        totalPatents++;
        
        emit PatentUploaded(patentId, msg.sender, name);
        
        return patentId;
    }
    
    /**
     * @dev Approve a patent (reviewer/admin only)
     */
    function approvePatent(uint256 patentId, string memory comments) 
        external 
        onlyReviewerOrAdmin 
        patentExists(patentId) 
    {
        require(patents[patentId].status == PatentStatus.PENDING, "Patent is not pending review");
        
        patents[patentId].status = PatentStatus.APPROVED;
        patents[patentId].reviewedBy = msg.sender;
        patents[patentId].reviewComments = comments;
        patents[patentId].reviewDate = block.timestamp;
        
        emit PatentApproved(patentId, msg.sender);
    }
    
    /**
     * @dev Reject a patent (reviewer/admin only)
     */
    function rejectPatent(uint256 patentId, string memory reason) 
        external 
        onlyReviewerOrAdmin 
        patentExists(patentId) 
    {
        require(patents[patentId].status == PatentStatus.PENDING, "Patent is not pending review");
        
        patents[patentId].status = PatentStatus.REJECTED;
        patents[patentId].reviewedBy = msg.sender;
        patents[patentId].reviewComments = reason;
        patents[patentId].reviewDate = block.timestamp;
        
        emit PatentRejected(patentId, msg.sender, reason);
    }
    
    /**
     * @dev Withdraw patent from trading
     */
    function withdrawPatent(uint256 patentId, string memory reason) 
        external 
        onlyPatentOwner(patentId) 
        patentExists(patentId) 
    {
        require(
            patents[patentId].status == PatentStatus.APPROVED || 
            patents[patentId].status == PatentStatus.NORMAL,
            "Patent cannot be withdrawn"
        );
        
        patents[patentId].status = PatentStatus.WITHDRAWN;
        
        emit PatentWithdrawn(patentId, msg.sender, reason);
    }
    
    /**
     * @dev Restore patent to trading
     */
    function restorePatent(uint256 patentId) 
        external 
        onlyPatentOwner(patentId) 
        patentExists(patentId) 
    {
        require(patents[patentId].status == PatentStatus.WITHDRAWN, "Patent is not withdrawn");
        
        patents[patentId].status = PatentStatus.NORMAL;
        
        emit PatentRestored(patentId, msg.sender);
    }
    
    /**
     * @dev Increment view count
     */
    function incrementViewCount(uint256 patentId) external patentExists(patentId) {
        patents[patentId].viewCount++;
        emit PatentViewed(patentId, msg.sender);
    }
    
    /**
     * @dev Increment download count
     */
    function incrementDownloadCount(uint256 patentId) external patentExists(patentId) {
        patents[patentId].downloadCount++;
        emit PatentDownloaded(patentId, msg.sender);
    }
    
    /**
     * @dev Get patent details
     */
    function getPatent(uint256 patentId) external view patentExists(patentId) returns (Patent memory) {
        return patents[patentId];
    }
    
    /**
     * @dev Get patents by user
     */
    function getUserPatents(address userAddress) external view returns (uint256[] memory) {
        return userPatents[userAddress];
    }
    
    /**
     * @dev Get pending patents for review
     */
    function getPendingPatents() external view onlyReviewerOrAdmin returns (uint256[] memory) {
        uint256[] memory pendingPatents = new uint256[](totalPatents);
        uint256 count = 0;
        
        for (uint256 i = 1; i < nextPatentId; i++) {
            if (patents[i].status == PatentStatus.PENDING) {
                pendingPatents[count] = i;
                count++;
            }
        }
        
        // Resize array to actual count
        uint256[] memory result = new uint256[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = pendingPatents[i];
        }
        
        return result;
    }
    
    /**
     * @dev Get total patents count
     */
    function getTotalPatents() external view returns (uint256) {
        return totalPatents;
    }
    
    /**
     * @dev Check if patent number exists
     */
    function patentNumberExists(string memory number) external view returns (bool) {
        return patentNumbers[number];
    }
}
