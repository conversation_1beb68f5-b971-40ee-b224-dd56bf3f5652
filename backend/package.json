{"name": "patent-exchange-backend", "version": "1.0.0", "description": "Blockchain-based patent exchange platform backend", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "compile-contracts": "truffle compile", "migrate": "truffle migrate", "migrate:reset": "truffle migrate --reset", "ganache": "ganache-cli -p 7545 -h 0.0.0.0 --deterministic --accounts 10 --defaultBalanceEther 100", "deploy": "npm run compile-contracts && npm run migrate"}, "keywords": ["blockchain", "patent", "exchange", "ethereum", "ipfs", "smart-contracts"], "author": "Patent Exchange Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "web3": "^4.3.0", "ipfs-http-client": "^60.0.1", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "morgan": "^1.10.0", "compression": "^1.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "uuid": "^9.0.1", "axios": "^1.6.2", "form-data": "^4.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "truffle": "^5.11.5", "ganache-cli": "^6.12.2", "@truffle/hdwallet-provider": "^2.1.15"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}