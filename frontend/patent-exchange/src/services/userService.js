import api from './apiClient.js'

// User service for managing user information and profiles
export const userService = {
  // Get user profile information by blockchain address
  async getUserProfile(address) {
    try {
      const response = await api.user.getProfile(address)
      return response.data.data
    } catch (error) {
      console.error('获取用户资料失败:', error)
      throw new Error(error.message || '获取用户资料失败')
    }
  },

  // Update user profile information
  async updateUserProfile(address, profileData) {
    try {
      const response = await api.user.updateProfile(address, profileData)
      return response.data
    } catch (error) {
      console.error('更新用户资料失败:', error)
      throw new Error(error.message || '更新用户资料失败')
    }
  },

  // Get user details for display in modals (when clicking blockchain addresses)
  async getUserDetails(address) {
    try {
      const response = await api.user.getProfile(address)
      return response.data.data
    } catch (error) {
      console.error('获取用户详情失败:', error)
      // Return fallback data if user not found
      return {
        name: '未知用户',
        phone: '未知',
        idNumber: '未知',
        address: address
      }
    }
  },

  // Get all users (admin only)
  async getAllUsers(params = {}) {
    try {
      const response = await api.user.getAll(params)
      return response.data.data.users || []
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw new Error(error.message || '获取用户列表失败')
    }
  },

  // Update user role (admin only)
  async updateUserRole(address, newRole, updatedBy) {
    try {
      const response = await api.user.updateRole({
        address,
        role: newRole,
        updatedBy
      })
      return response.data
    } catch (error) {
      console.error('更新用户角色失败:', error)
      throw new Error(error.message || '更新用户角色失败')
    }
  },

  // Get user statistics (admin only)
  async getUserStatistics() {
    try {
      const response = await api.user.getStatistics()
      return response.data.data
    } catch (error) {
      console.error('获取用户统计失败:', error)
      throw new Error(error.message || '获取用户统计失败')
    }
  },

  // Validate user profile data
  validateProfile(profileData) {
    const errors = []

    if (!profileData.name || profileData.name.trim().length < 2) {
      errors.push('姓名至少需要2个字符')
    }

    if (!profileData.phone || !/^1[3-9]\d{9}$/.test(profileData.phone)) {
      errors.push('请输入有效的手机号码')
    }

    if (!profileData.idNumber || !/^\d{17}[\dXx]$/.test(profileData.idNumber)) {
      errors.push('请输入有效的身份证号码')
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  },

  // Validate role change
  validateRoleChange(currentRole, newRole, userRole) {
    const errors = []

    // Only admins can change roles
    if (userRole !== 'admin') {
      errors.push('只有管理员可以修改用户角色')
    }

    // Valid roles
    const validRoles = ['user', 'reviewer', 'admin']
    if (!validRoles.includes(newRole)) {
      errors.push('无效的角色类型')
    }

    // Cannot change own role
    if (currentRole === 'admin' && newRole !== 'admin') {
      errors.push('管理员不能降级自己的角色')
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }
}
