<template>
  <div class="patent-protection-view">
    <div class="container-fluid py-4">
      <div class="d-flex align-items-center mb-4">
        <i class="bi bi-shield-exclamation text-primary me-3" style="font-size: 2rem;"></i>
        <div>
          <h2 class="mb-1">专利维权</h2>
          <p class="text-muted mb-0">专利权益保护</p>
        </div>
      </div>

      <!-- Tab Navigation -->
      <ul class="nav nav-tabs mb-4" id="protectionTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link active"
            id="my-cases-tab"
            data-bs-toggle="tab"
            data-bs-target="#my-cases"
            type="button"
            role="tab"
          >
            <i class="bi bi-list-ul me-2"></i>
            我的维权案例
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            id="initiate-protection-tab"
            data-bs-toggle="tab"
            data-bs-target="#initiate-protection"
            type="button"
            role="tab"
          >
            <i class="bi bi-plus-circle me-2"></i>
            发起维权
          </button>
        </li>
      </ul>

      <!-- Tab Content -->
      <div class="tab-content" id="protectionTabsContent">
        <!-- My Cases Tab -->
        <div class="tab-pane fade show active" id="my-cases" role="tabpanel">
          <!-- Loading State -->
          <div v-if="isLoadingCases" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="text-muted mt-3">正在加载维权案例...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="casesError" class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {{ casesError }}
          </div>

          <!-- Cases List -->
          <div v-else-if="myCases.length > 0" class="row">
            <div v-for="case_ in myCases" :key="case_.id" class="col-lg-6 mb-4">
              <div class="card shadow-sm h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">{{ case_.patentName }}</h6>
                  <span :class="getStatusBadgeClass(case_.status)" class="badge">
                    {{ getStatusText(case_.status) }}
                  </span>
                </div>
                <div class="card-body">
                  <div class="row g-2 mb-3">
                    <div class="col-6">
                      <small class="text-muted">专利号</small>
                      <div class="fw-bold small">{{ case_.patentNumber }}</div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">提交时间</small>
                      <div class="small">{{ formatDate(case_.submitDate) }}</div>
                    </div>
                    <div class="col-12">
                      <small class="text-muted">维权描述</small>
                      <div class="small text-truncate">{{ case_.description }}</div>
                    </div>
                  </div>

                  <div class="d-flex justify-content-between">
                    <button
                      class="btn btn-outline-primary btn-sm"
                      @click="viewCaseDetails(case_)"
                    >
                      <i class="bi bi-eye me-1"></i>
                      查看详情
                    </button>
                    <button
                      v-if="case_.evidenceHash"
                      class="btn btn-outline-info btn-sm"
                      @click="downloadEvidence(case_.evidenceHash)"
                    >
                      <i class="bi bi-download me-1"></i>
                      下载证据
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-5">
            <i class="bi bi-shield-check text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">暂无维权案例</h4>
            <p class="text-muted">您还没有发起过专利维权申请</p>
            <button class="btn btn-primary" @click="switchToInitiateTab">
              <i class="bi bi-plus-circle me-2"></i>
              发起维权申请
            </button>
          </div>
        </div>

        <!-- Initiate Protection Tab -->
        <div class="tab-pane fade" id="initiate-protection" role="tabpanel">
          <div class="row">
            <div class="col-lg-8">
              <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                  <h5 class="mb-0">
                    <i class="bi bi-shield-exclamation me-2"></i>
                    发起专利维权申请
                  </h5>
                </div>
                <div class="card-body">
                  <form @submit.prevent="submitProtection">
                    <div class="mb-3">
                      <label for="patentSearch" class="form-label">专利信息 <span class="text-danger">*</span></label>
                      <div class="input-group">
                        <input
                          type="text"
                          class="form-control"
                          id="patentSearch"
                          v-model="protectionForm.patentQuery"
                          placeholder="输入专利名称或专利号进行搜索"
                          :class="{ 'is-invalid': validationErrors.patent }"
                        >
                        <button
                          class="btn btn-outline-secondary"
                          type="button"
                          @click="searchPatent"
                          :disabled="isSearching"
                        >
                          <span v-if="isSearching" class="spinner-border spinner-border-sm me-2"></span>
                          <i v-else class="bi bi-search me-2"></i>
                          搜索
                        </button>
                      </div>
                      <div v-if="validationErrors.patent" class="invalid-feedback">
                        {{ validationErrors.patent }}
                      </div>
                    </div>

                    <!-- Selected Patent Display -->
                    <div v-if="selectedPatent" class="mb-3">
                      <div class="alert alert-info">
                        <h6 class="alert-heading">已选择专利</h6>
                        <div class="row g-2">
                          <div class="col-md-6">
                            <strong>专利名称:</strong> {{ selectedPatent.name }}
                          </div>
                          <div class="col-md-6">
                            <strong>专利号:</strong> {{ selectedPatent.number }}
                          </div>
                          <div class="col-md-6">
                            <strong>当前权利人:</strong> {{ selectedPatent.ownerName }}
                          </div>
                          <div class="col-md-6">
                            <strong>区块链地址:</strong> {{ formatAddress(selectedPatent.uploaderAddress) }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="mb-3">
                      <label for="description" class="form-label">维权描述 <span class="text-danger">*</span></label>
                      <textarea
                        class="form-control"
                        id="description"
                        rows="5"
                        v-model="protectionForm.description"
                        placeholder="请详细描述您的专利权利主张，包括您拥有该专利的证据和理由..."
                        :class="{ 'is-invalid': validationErrors.description }"
                      ></textarea>
                      <div v-if="validationErrors.description" class="invalid-feedback">
                        {{ validationErrors.description }}
                      </div>
                    </div>

                    <div class="mb-3">
                      <label for="evidenceFile" class="form-label">证据文件 <span class="text-danger">*</span></label>
                      <input
                        type="file"
                        class="form-control"
                        id="evidenceFile"
                        @change="handleFileUpload"
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        :class="{ 'is-invalid': validationErrors.evidence }"
                      >
                      <div class="form-text">
                        支持格式: PDF, Word文档, 图片 (最大10MB)
                      </div>
                      <div v-if="validationErrors.evidence" class="invalid-feedback">
                        {{ validationErrors.evidence }}
                      </div>
                    </div>

                    <div class="d-flex justify-content-between">
                      <button
                        type="button"
                        class="btn btn-outline-secondary"
                        @click="resetForm"
                        :disabled="isSubmitting"
                      >
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        重置
                      </button>
                      <button
                        type="submit"
                        class="btn btn-warning"
                        :disabled="isSubmitting || !selectedPatent"
                      >
                        <span v-if="isSubmitting" class="spinner-border spinner-border-sm me-2"></span>
                        <i v-else class="bi bi-shield-exclamation me-2"></i>
                        {{ isSubmitting ? '提交中...' : '提交维权申请' }}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <div class="col-lg-4">
              <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                  <h6 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    维权须知
                  </h6>
                </div>
                <div class="card-body">
                  <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                      <i class="bi bi-check-circle text-success me-2"></i>
                      请确保您拥有该专利的合法权利
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle text-success me-2"></i>
                      提供充分的证据支持您的权利主张
                    </li>
                    <li class="mb-2">
                      <i class="bi bi-check-circle text-success me-2"></i>
                      维权申请将由专业审核员进行审核
                    </li>
                    <li class="mb-0">
                      <i class="bi bi-check-circle text-success me-2"></i>
                      审核通过后专利权将自动转移
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Success Message -->
      <div v-if="successMessage" class="alert alert-success alert-dismissible fade show mt-3" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        {{ successMessage }}
        <button type="button" class="btn-close" @click="successMessage = null"></button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { transactionService } from '@/services/transactionService'
import { patentService } from '@/services/patentService'

export default {
  name: 'PatentProtectionView',
  setup() {
    const route = useRoute()
    const authStore = useAuthStore()

    // State for cases
    const isLoadingCases = ref(false)
    const casesError = ref(null)
    const myCases = ref([])

    // State for form
    const isSearching = ref(false)
    const isSubmitting = ref(false)
    const successMessage = ref(null)
    const selectedPatent = ref(null)

    // Form data
    const protectionForm = reactive({
      patentQuery: '',
      description: '',
      evidenceFile: null
    })

    // Validation errors
    const validationErrors = ref({})

    // Load user's protection cases
    const loadMyCases = async () => {
      try {
        isLoadingCases.value = true
        casesError.value = null

        // Mock data - in real app, this would come from API
        myCases.value = [
          {
            id: 'case_001',
            patentId: '1',
            patentName: '智能手机充电技术',
            patentNumber: 'CN202410001234.5',
            description: '我是该专利的真正发明人，有充分证据证明专利权归属。',
            submitDate: '2024-01-20 10:30:00',
            status: 'pending',
            evidenceHash: 'QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'
          }
        ]

      } catch (err) {
        casesError.value = err.message
      } finally {
        isLoadingCases.value = false
      }
    }

    // Search patent
    const searchPatent = async () => {
      try {
        if (!protectionForm.patentQuery.trim()) {
          validationErrors.value.patent = '请输入专利名称或专利号'
          return
        }

        isSearching.value = true
        validationErrors.value = {}

        const result = await patentService.searchPatents({
          name: protectionForm.patentQuery,
          number: protectionForm.patentQuery
        })

        if (result.patents.length > 0) {
          selectedPatent.value = result.patents[0]
        } else {
          validationErrors.value.patent = '未找到匹配的专利'
        }

      } catch (err) {
        validationErrors.value.patent = err.message
      } finally {
        isSearching.value = false
      }
    }

    // Handle file upload
    const handleFileUpload = (event) => {
      const file = event.target.files[0]
      if (file) {
        // Validate file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
          validationErrors.value.evidence = '文件大小不能超过10MB'
          return
        }

        protectionForm.evidenceFile = file
        validationErrors.value.evidence = null
      }
    }

    // Submit protection
    const submitProtection = async () => {
      try {
        // Validate form
        const errors = {}

        if (!selectedPatent.value) {
          errors.patent = '请先搜索并选择专利'
        }

        if (!protectionForm.description.trim()) {
          errors.description = '请输入维权描述'
        }

        if (!protectionForm.evidenceFile) {
          errors.evidence = '请上传证据文件'
        }

        if (Object.keys(errors).length > 0) {
          validationErrors.value = errors
          return
        }

        isSubmitting.value = true
        validationErrors.value = {}

        const protectionData = {
          patentId: selectedPatent.value.id,
          claimantAddress: authStore.account,
          description: protectionForm.description,
          evidenceFile: protectionForm.evidenceFile
        }

        const result = await transactionService.initiateRightsProtection(protectionData)

        if (result.success) {
          successMessage.value = result.message
          resetForm()
          await loadMyCases()

          // Switch to cases tab
          const tab = new bootstrap.Tab(document.getElementById('my-cases-tab'))
          tab.show()
        }

      } catch (err) {
        casesError.value = err.message
      } finally {
        isSubmitting.value = false
      }
    }

    // Reset form
    const resetForm = () => {
      protectionForm.patentQuery = ''
      protectionForm.description = ''
      protectionForm.evidenceFile = null
      selectedPatent.value = null
      validationErrors.value = {}

      // Reset file input
      const fileInput = document.getElementById('evidenceFile')
      if (fileInput) fileInput.value = ''
    }

    // View case details
    const viewCaseDetails = (case_) => {
      console.log('查看案例详情:', case_)
      // TODO: Implement case details modal or page
    }

    // Download evidence
    const downloadEvidence = (evidenceHash) => {
      console.log('下载证据:', evidenceHash)
      // TODO: Implement evidence download from IPFS
    }

    // Switch to initiate tab
    const switchToInitiateTab = () => {
      const tab = new bootstrap.Tab(document.getElementById('initiate-protection-tab'))
      tab.show()
    }

    // Utility functions
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const formatAddress = (address) => {
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    const getStatusText = (status) => {
      const statusMap = {
        'pending': '待审核',
        'approved': '已批准',
        'rejected': '已拒绝'
      }
      return statusMap[status] || '未知'
    }

    const getStatusBadgeClass = (status) => {
      const classMap = {
        'pending': 'bg-warning',
        'approved': 'bg-success',
        'rejected': 'bg-danger'
      }
      return classMap[status] || 'bg-secondary'
    }

    // Initialize
    onMounted(() => {
      if (authStore.isConnected) {
        loadMyCases()
      }

      // Check if patent ID is provided in query params
      if (route.query.patentId) {
        protectionForm.patentQuery = route.query.patentId
        searchPatent()

        // Switch to initiate tab
        setTimeout(() => {
          switchToInitiateTab()
        }, 100)
      }
    })

    return {
      isLoadingCases,
      casesError,
      myCases,
      isSearching,
      isSubmitting,
      successMessage,
      selectedPatent,
      protectionForm,
      validationErrors,
      searchPatent,
      handleFileUpload,
      submitProtection,
      resetForm,
      viewCaseDetails,
      downloadEvidence,
      switchToInitiateTab,
      formatDate,
      formatAddress,
      getStatusText,
      getStatusBadgeClass
    }
  }
}
</script>

<style scoped>
.patent-protection-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 10px;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

.nav-tabs .nav-link {
  border-radius: 8px 8px 0 0;
}

.nav-tabs .nav-link.active {
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}

.btn {
  border-radius: 6px;
}

.alert {
  border-radius: 8px;
}

.badge {
  border-radius: 6px;
}
</style>
