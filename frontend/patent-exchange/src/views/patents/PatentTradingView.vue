<template>
  <div class="patent-trading-view">
    <div class="container-fluid py-4">
      <div class="d-flex align-items-center mb-4">
        <i class="bi bi-arrow-left-right text-primary me-3" style="font-size: 2rem;"></i>
        <div>
          <h2 class="mb-1">专利交易</h2>
          <p class="text-muted mb-0">管理您的专利交易记录</p>
        </div>
      </div>

      <!-- Tab Navigation -->
      <ul class="nav nav-tabs mb-4" id="tradingTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link active"
            id="my-transactions-tab"
            data-bs-toggle="tab"
            data-bs-target="#my-transactions"
            type="button"
            role="tab"
          >
            <i class="bi bi-list-ul me-2"></i>
            我的交易
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            id="available-patents-tab"
            data-bs-toggle="tab"
            data-bs-target="#available-patents"
            type="button"
            role="tab"
          >
            <i class="bi bi-search me-2"></i>
            可购买专利
          </button>
        </li>
      </ul>

      <!-- Tab Content -->
      <div class="tab-content" id="tradingTabsContent">
        <!-- My Transactions Tab -->
        <div class="tab-pane fade show active" id="my-transactions" role="tabpanel">
          <!-- Loading State -->
          <div v-if="isLoadingTransactions" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="text-muted mt-3">正在加载交易记录...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="transactionError" class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {{ transactionError }}
          </div>

          <!-- Transactions List -->
          <div v-else-if="userTransactions.length > 0" class="row">
            <div v-for="transaction in userTransactions" :key="transaction.id" class="col-lg-6 mb-4">
              <div class="card shadow-sm h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">{{ transaction.patentName }}</h6>
                  <span :class="getTransactionStatusBadge(transaction.status)" class="badge">
                    {{ getTransactionStatusText(transaction.status) }}
                  </span>
                </div>
                <div class="card-body">
                  <div class="row g-2 mb-3">
                    <div class="col-6">
                      <small class="text-muted">专利号</small>
                      <div class="fw-bold small">{{ transaction.patentNumber }}</div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">交易价格</small>
                      <div class="fw-bold text-success">¥{{ formatPrice(transaction.price) }}</div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">卖方</small>
                      <div>
                        <button
                          class="btn btn-link p-0 text-decoration-none small"
                          @click="showUserDetails(transaction.sellerAddress)"
                        >
                          {{ formatAddress(transaction.sellerAddress) }}
                          <i class="bi bi-box-arrow-up-right ms-1"></i>
                        </button>
                      </div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">提交时间</small>
                      <div class="small">{{ formatDate(transaction.submitDate) }}</div>
                    </div>
                  </div>

                  <div class="d-flex justify-content-between">
                    <button
                      class="btn btn-outline-primary btn-sm"
                      @click="viewPatentDetails(transaction.patentId)"
                    >
                      <i class="bi bi-eye me-1"></i>
                      查看专利
                    </button>
                    <button
                      v-if="transaction.status === 'approved'"
                      class="btn btn-success btn-sm"
                      @click="downloadContract(transaction.id)"
                    >
                      <i class="bi bi-download me-1"></i>
                      下载合同
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-5">
            <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">暂无交易记录</h4>
            <p class="text-muted">您还没有进行过任何专利交易</p>
            <button class="btn btn-primary" @click="switchToAvailableTab">
              <i class="bi bi-search me-2"></i>
              浏览可购买专利
            </button>
          </div>
        </div>

        <!-- Available Patents Tab -->
        <div class="tab-pane fade" id="available-patents" role="tabpanel">
          <!-- Search Filters -->
          <div class="card shadow-sm mb-4">
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-4">
                  <label for="searchName" class="form-label">专利名称</label>
                  <input
                    type="text"
                    class="form-control"
                    id="searchName"
                    v-model="searchFilters.name"
                    placeholder="输入专利名称"
                  >
                </div>
                <div class="col-md-4">
                  <label for="searchNumber" class="form-label">专利号</label>
                  <input
                    type="text"
                    class="form-control"
                    id="searchNumber"
                    v-model="searchFilters.number"
                    placeholder="输入专利号"
                  >
                </div>
                <div class="col-md-4">
                  <label for="searchCategory" class="form-label">专利类别</label>
                  <select class="form-select" id="searchCategory" v-model="searchFilters.category">
                    <option value="">全部类别</option>
                    <option value="电子技术">电子技术</option>
                    <option value="智能控制">智能控制</option>
                    <option value="人工智能">人工智能</option>
                    <option value="区块链">区块链</option>
                  </select>
                </div>
                <div class="col-12">
                  <button class="btn btn-primary me-2" @click="searchPatents">
                    <i class="bi bi-search me-2"></i>
                    搜索
                  </button>
                  <button class="btn btn-outline-secondary" @click="resetSearch">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    重置
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="isLoadingPatents" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="text-muted mt-3">正在搜索专利...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="patentError" class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {{ patentError }}
          </div>

          <!-- Patents List -->
          <div v-else-if="availablePatents.length > 0" class="row">
            <div v-for="patent in availablePatents" :key="patent.id" class="col-lg-6 mb-4">
              <div class="card shadow-sm h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">{{ patent.name }}</h6>
                  <span class="badge bg-primary">{{ patent.category }}</span>
                </div>
                <div class="card-body">
                  <div class="row g-2 mb-3">
                    <div class="col-6">
                      <small class="text-muted">专利号</small>
                      <div class="fw-bold small">{{ patent.number }}</div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">转让价格</small>
                      <div class="fw-bold text-success">¥{{ formatPrice(patent.price) }}</div>
                    </div>
                    <div class="col-12">
                      <small class="text-muted">上传者</small>
                      <div>
                        <button
                          class="btn btn-link p-0 text-decoration-none small"
                          @click="showUserDetails(patent.uploaderAddress)"
                        >
                          {{ formatAddress(patent.uploaderAddress) }}
                          <i class="bi bi-box-arrow-up-right ms-1"></i>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="d-flex justify-content-between">
                    <button
                      class="btn btn-outline-primary btn-sm"
                      @click="viewPatentDetails(patent.id)"
                    >
                      <i class="bi bi-eye me-1"></i>
                      查看详情
                    </button>
                    <button
                      class="btn btn-success btn-sm"
                      @click="quickPurchase(patent)"
                      :disabled="patent.uploaderAddress === authStore.account"
                    >
                      <i class="bi bi-cart-plus me-1"></i>
                      立即购买
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-5">
            <i class="bi bi-search text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">未找到专利</h4>
            <p class="text-muted">请尝试调整搜索条件</p>
          </div>
        </div>
      </div>

      <!-- Success Message -->
      <div v-if="successMessage" class="alert alert-success alert-dismissible fade show mt-3" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        {{ successMessage }}
        <button type="button" class="btn-close" @click="successMessage = null"></button>
      </div>
    </div>

    <!-- User Detail Modal -->
    <UserDetailModal
      modal-id="userDetailModal"
      :user-address="selectedUserAddress"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { transactionService } from '@/services/transactionService'
import { patentService } from '@/services/patentService'
import UserDetailModal from '@/components/UserDetailModal.vue'

export default {
  name: 'PatentTradingView',
  components: {
    UserDetailModal
  },
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()

    // State for transactions
    const isLoadingTransactions = ref(false)
    const transactionError = ref(null)
    const userTransactions = ref([])

    // State for available patents
    const isLoadingPatents = ref(false)
    const patentError = ref(null)
    const availablePatents = ref([])

    // Search filters
    const searchFilters = reactive({
      name: '',
      number: '',
      category: ''
    })

    // UI state
    const successMessage = ref(null)
    const selectedUserAddress = ref(null)

    // Load user transactions
    const loadUserTransactions = async () => {
      try {
        isLoadingTransactions.value = true
        transactionError.value = null

        const transactions = await transactionService.getUserTransactions(authStore.account)
        userTransactions.value = transactions

      } catch (err) {
        transactionError.value = err.message
      } finally {
        isLoadingTransactions.value = false
      }
    }

    // Search patents
    const searchPatents = async () => {
      try {
        isLoadingPatents.value = true
        patentError.value = null

        const result = await patentService.searchPatents(searchFilters)
        availablePatents.value = result.patents

      } catch (err) {
        patentError.value = err.message
      } finally {
        isLoadingPatents.value = false
      }
    }

    // Reset search
    const resetSearch = () => {
      searchFilters.name = ''
      searchFilters.number = ''
      searchFilters.category = ''
      searchPatents()
    }

    // Quick purchase
    const quickPurchase = async (patent) => {
      try {
        const transactionData = {
          patentId: patent.id,
          buyerAddress: authStore.account,
          sellerAddress: patent.uploaderAddress,
          price: patent.price
        }

        const result = await transactionService.initiateTransaction(transactionData)

        if (result.success) {
          successMessage.value = result.message
          // Refresh transactions
          await loadUserTransactions()
        }

      } catch (err) {
        transactionError.value = err.message
      }
    }

    // View patent details
    const viewPatentDetails = (patentId) => {
      router.push({ name: 'patent-detail', params: { id: patentId } })
    }

    // Show user details modal
    const showUserDetails = (address) => {
      selectedUserAddress.value = address
      const modal = new bootstrap.Modal(document.getElementById('userDetailModal'))
      modal.show()
    }

    // Download contract
    const downloadContract = (transactionId) => {
      console.log('下载合同:', transactionId)
      // TODO: Implement contract download
    }

    // Switch to available patents tab
    const switchToAvailableTab = () => {
      const tab = new bootstrap.Tab(document.getElementById('available-patents-tab'))
      tab.show()
      if (availablePatents.value.length === 0) {
        searchPatents()
      }
    }

    // Utility functions
    const formatPrice = (price) => {
      return new Intl.NumberFormat('zh-CN').format(price)
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const formatAddress = (address) => {
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    const getTransactionStatusText = (status) => {
      const statusMap = {
        'pending': '待审核',
        'approved': '已批准',
        'rejected': '已拒绝'
      }
      return statusMap[status] || '未知'
    }

    const getTransactionStatusBadge = (status) => {
      const classMap = {
        'pending': 'bg-warning',
        'approved': 'bg-success',
        'rejected': 'bg-danger'
      }
      return classMap[status] || 'bg-secondary'
    }

    // Initialize
    onMounted(() => {
      if (authStore.isConnected) {
        loadUserTransactions()
        searchPatents()
      }
    })

    return {
      authStore,
      isLoadingTransactions,
      transactionError,
      userTransactions,
      isLoadingPatents,
      patentError,
      availablePatents,
      searchFilters,
      successMessage,
      selectedUserAddress,
      searchPatents,
      resetSearch,
      quickPurchase,
      viewPatentDetails,
      showUserDetails,
      downloadContract,
      switchToAvailableTab,
      formatPrice,
      formatDate,
      formatAddress,
      getTransactionStatusText,
      getTransactionStatusBadge
    }
  }
}
</script>

<style scoped>
.patent-trading-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 10px;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

.nav-tabs .nav-link {
  border-radius: 8px 8px 0 0;
}

.nav-tabs .nav-link.active {
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}

.btn {
  border-radius: 6px;
}

.alert {
  border-radius: 8px;
}

.badge {
  border-radius: 6px;
}
</style>
